<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" class="form-control" name="row[name]" type="text" value="{$row.name}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Desc')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-desc" class="form-control " rows="5" name="row[desc]" cols="50">{$row.desc}</textarea>
        </div>
    </div>

    <!-- ++ 新增内容开始 ++ -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('小程序首页路径')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-wx_page_path" class="form-control" name="row[wx_page_path]" type="text" placeholder="例如: pages/customerA/index" value="{$row.wx_page_path}">
            <span class="help-block">请填写客户对应的小程序页面路径，留空则使用默认首页。</span>
        </div>
    </div>
    <!-- ++ 新增内容结束 ++ -->
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>

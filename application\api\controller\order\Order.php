<?php

namespace app\api\controller\order;

use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use app\common\logic\Equipment;
use fast\Random;
use think\Validate;
use weixin\WXBizDataCrypt;
use weixin\WeixinPay;
use app\api\controller\Wechat;

/**
 * 订单接口
 */
class Order extends Api {

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';

    public function _initialize() {
        trace('初始化开始');
        parent::_initialize();
        trace('初始化结束');
    }

    /*
     * 创建订单
     * id 床id
     */

    public function orderAdd($id, $user_id, $nb_goodsid = '') {
        $return = [
            'success' => false,
            'msg' => '',
            'data' => '',
        ];
        if ($id) {
            //查询地锁信息
            $equipment = db('equipment')->where(['id' => $id])->find();
//pd($equipment);
            if ($equipment) {
                //查询主设备信息
//                $equipment = db('equipment')->where(['id' => $equipment_info['equipment_id']])->find();
                if ($equipment['hardware_type'] == 1) {//mqtt地锁设备
                    //查询这个设备是否有状态为正在使用中的订单
                    $order_conduct = db('order')->where(['equipment_id' => $equipment['id'], 'status' => 1, 'user_id' => ['<>', $user_id], 'use_status' => 2])->find();
                    if ($order_conduct) {
                        //有未结束的订单
//                        $return = $this->orderEnd($order_conduct);
//                        if ($return['success']) {
//                            $order_conduct = $return['data'];
//                            $Wechat = new Wechat();
//                            $orenid = db('user')->where(['id' => $order_conduct['user_id']])->value('openid');
//                            $template_id = 'SY6bRx-PVZ7mfu1R5ljuNby_Wok7JveHlXzg55WFiqY';
//                            $url = 'pages/index/index';
//                            $data = array(
//                                'character_string1' => array('value' => $order_conduct['sn'],),
//                                'amount2' => array('value' => $order_conduct['money'] . '元',),
//                                'date4' => array('value' => date('Y-m-d H:i:s', $order_conduct['createtime']),),
//                                'date5' => array('value' => date('Y-m-d H:i:s', $order_conduct['returntime']),),
//                                'phrase3' => array('value' => '本次服务完成,请及时支付！！',),
//                            );
//                            $return = $Wechat->newsSendout($orenid, $template_id, $url, $data);
//                        }
                        $this->error('这个地锁已经有人在使用了');
                    }
                }
                if ($equipment['use_status'] == 1) {

                    //调用关锁方法,关闭地锁,开始订单
                    $equipmentLogic = new Equipment();
                    //这里根据设备所属的客户不同,下发不同的开锁指令, 重庆客户是二轮地锁,默认是开启状态,所以开始订单应该下发关锁指令
                    //其他客户目前使用的是四轮地锁,默认是关闭(升起的状态),所以开始订单,应该下发开锁指令

                    $result = $equipmentLogic->sendSwitchCommand($equipment['sn'], 'close');
                    if ($result['code'] != 1) {
                        $return['msg'] = $result['msg'];
                        return $return;
                    }

                    //医院信息
                    $hospital = db('hospital')->where(['id' => $equipment['hospitals_id']])->find();

                    $info = $this->equipment_info_lujing($equipment);

                    $is_maintain = db('user')->where(['id' => $user_id])->value('is_maintain');
                    if ($is_maintain == 1) {

                        $order = db('order')->where(['user_id' => $user_id, 'status' => ['<>', 3], 'use_status' => 2])->find();
                        if (!$order) {
                            $order = array(
                                'platform_id' => $equipment['platform_id'],
                                'agent_id' => $equipment['agent_id'],
                                'hospital_id' => $equipment['hospitals_id'],
                                'hospital_fcbl' => $hospital['fcbl'],
                                'hospital_price' => $hospital['price'],
                                'hospital_hourlong' => $hospital['hourlong'],
                                'hospital_freedt' => $hospital['freedt'],
                                'departments_id' => $equipment['departments_id'],
                                'equipment_id' => $equipment['id'],
//                                'equipment_info_id' => $equipment_info['id'],
                                'info' => $info,
                                'sn' => $this->getOrdersn(),
                                'user_id' => $user_id,
                                'status' => 1,
                                'pay_types' => 0,
                                'pay_status' => 0,
                                //'createtime' => time(),
                                'updatetime' => time(),
                                'use_status' => 1,
                                'nb_goodsid' => $nb_goodsid,
                            );
                            $answer_return_time = '';
                            if ($hospital['use_start'] != '' && $hospital['use_end'] != '') {
                                //医院上设置了使用限制
                                $h = date('H');
                                if ($hospital['use_start'] > $hospital['use_end']) {
                                    $answer_return_time = strtotime(date('Y-m-d ' . $hospital['use_end'] . ':00:00', strtotime("+1 day")));
                                } else {
                                    $answer_return_time = strtotime(date('Y-m-d ' . $hospital['use_end'] . ':00:00'));
                                }
                            }
                            $order['answer_return_time'] = $answer_return_time;
                            if ($equipment['hardware_type'] == 2) {
                                $order['use_status'] = 2;
                                $order['createtime'] = time();
                            }
                            $res = db('order')->insertGetId($order);
                            if ($res) {
                                
                                $return['success'] = true;
                                $return['msg'] = '生成订单成功';
                                $return['data'] = $res;
                            } else {
                                $return['msg'] = '生成订单失败';
                            }
                        } else {
                            $return['msg'] = '您的订单以生成';
                        }
                    } else {
                        $maintain_data = array(
                            'platform_id' => $equipment['platform_id'],
                            'agent_id' => $equipment['agent_id'],
                            'hospital_id' => $equipment['hospitals_id'],
                            'departments_id' => $equipment['departments_id'],
                            'equipment_id' => $equipment['id'],
//                            'equipment_info_id' => $equipment_info['id'],
                            'info' => $info,
                            'user_id' => $user_id,
                            'createtime' => time(),
                        );
                        $res = db('maintain')->insertGetId($maintain_data);
                        if ($res) {
//                            db('equipment_info')->where(['id' => $id])->update(array('status' => 2));
                            $return['msg'] = '生成维护记录成功';
                        } else {
                            $return['msg'] = '生成维护记录失败';
                        }
                    }
                } else {
                    $return['msg'] = '主设备被禁用';
                }
            } else {
                $return['msg'] = '设备不存在';
            }
        } else {
            $return['msg'] = '参数错误';
        }
        return $return;
    }

    //随机生成订单编号
    private function getOrdersn($surface = 'order') {
        $no = 'ord' . date('YmdHis') . rand(10000000, 99999999);
        if (db($surface)->where('sn', $no)->find()) {
            $no = $this->getOrdersn();
        } else {
            return $no;
        }
    }

    /**
     * 处理结束订单的逻辑
     * @param array $order_info 订单信息
     */
    public function orderEnd($order_info) {

        $return = array(
            'success' => true,
            'msg' => '',
        );
        $time = time();
        if ($order_info['status'] == 1) {
            $timelong = ceil(($time - $order_info['createtime']) / 3600); //计算时长（小时）
            $timelong_fenzhong = ceil(($time - $order_info['createtime']) / 60); //计算时长（分钟）
            $overtime = 0; //超时时长
            $overtime_money = 0; //超时费用
            $normal_money = 0; //时间段内归还费用

            $hospital = db('hospital')->where(['id' => $order_info['hospital_id']])->find();
            if ($hospital['charging_rule'] == 1) {
                //按小时计费
                if ($hospital['use_start'] != '' && $hospital['use_end'] != '') {
                    //开启了时间限制
                    if ($time <= $order_info['answer_return_time'] && $order_info['answer_return_time'] != '') {
                        //没有超时归还
                        $money = ($timelong / $order_info['hospital_hourlong']) * $order_info['hospital_price']; //计算费用（元）
                    } else {
                        //超时归还
                        $money = $normal_money = ( ceil(($order_info['answer_return_time'] - $order_info['createtime']) / 3600) / $order_info['hospital_hourlong']) * $order_info['hospital_price']; //计算费用（元）
                        //计算超时费用
                        $money += $overtime_money = ceil((( $time - $order_info['answer_/return_time']) / 3600)) * $hospital['overtime_price'];
                        //超时时长
                        $overtime = ceil((( $time - $order_info['answer_return_time']) / 3600));
                    }
                } else {
                    //没有开启时间限制
                    $money = ($timelong / $order_info['hospital_hourlong']) * $order_info['hospital_price']; //计算费用（元）
                }
            } else {
                //包干计费
                $money = $normal_money = $hospital['contract_price'];
                if ($hospital['use_start'] != '' && $hospital['use_end'] != '') {
                    if ($order_info['answer_return_time'] < $time && $order_info['answer_return_time'] != '') {
                        //超时归还
                        //计算超时费用
                        $money += $overtime_money = ceil((( $time - $order_info['answer_return_time']) / 3600)) * $hospital['overtime_price'];
                        $overtime = ceil((( $time - $order_info['answer_return_time']) / 3600));
                    }
                }
            }
            $order_update_data = array(
                'status' => 2,
                'returntime' => $time,
                'timelong' => $timelong,
                'timelong_fenzhong' => $timelong_fenzhong,
                'money' => $money,
                'really_money' => $money,
                'overtime' => $overtime,
                'overtime_money' => $overtime_money,
                'normal_money' => $normal_money,
                'charging_rule' => $hospital['charging_rule'],
            );
//            print_r($order_update_data);die();
            if ($timelong_fenzhong > $order_info['hospital_freedt']) {
                //如果使用时长大于免费时间
                $order_update_data['pay_status'] = 1;
            } else {
                $exemption_count = db('platform')->where(['id' => $order_info['platform_id']])->value('exemption_count');
                $order_exemption_count = db('order')->where([
                            'returntime' => array('between', array(
                                    strtotime(date('Y-m-d 00:00:00')),
                                    strtotime(date('Y-m-d 23:59:59'))
                            )),
                            'user_id' => $order_info['user_id'],
                            'pay_status' => 3
                        ])->count();
                if ($order_exemption_count < $exemption_count) {
                    $order_update_data['pay_status'] = 3;
                } else {
                    $order_update_data['pay_status'] = 1;
                }
            }
            if($order_update_data['pay_status'] == 1){//订单正常支付 判断一下用户是否套餐卡
                $log_where = [
                    'user_id' => $order_info['user_id'],
                    'effective_start' => ['<=',  time()],
                    'effective_end' => ['>=',  time()],
                    'deletetime' => NULL,
                    'status' => 2,
                ];
                $setmeal_log = db('setmeal_log')->where($log_where)->find();
                if($setmeal_log){
                    $order_update_data['pay_status'] = 5;
                }
            }
            $order_update_data['actreturntime'] = time();
            $res = db('order')->where(['id' => $order_info['id']])->update($order_update_data);
            if (!$res) {
                $return['success'] = false;
                $return['msg'] = '更新状态失败';
            } else {

                $hardware_type = db('equipment')->where(['id' => $order_info['equipment_id']])->value('hardware_type');
                if ($hardware_type == 1) {
                    $equipment_info_update_data = array(
                        'use_status' => 1,
                    );
                    //更新设备信息为 = 未租用
                    db('equipment')->where(['id' => $order_info['equipment_id']])->update($equipment_info_update_data);
                }
                //重新查询更改后的订单数据
                $order_info = db('order')->where(['id' => $order_info['id']])->find();
                $return['data'] = $order_info;
//                $user = db('user')->where(['id' => $order_info['user_id']])->find();
//                $Wechat = new Wechat();
//                $openid = $user['openid'];
//                $template_id = 'ky02g-3lrwJePjt8_sKwCPkhMFMQOZ9ND2-SA_TLmDo';
//                $url = 'pages/orders/orders?status=1';
//                $data = array(
//                    'time1' => array('value' => date('Y-m-d H:i:s',$order_info['createtime']),),
//                    'character_string2' => array('value' => $order_info['sn'],),
//                    'thing3' => array('value' => '共享商品租赁未结束订单',),
//                    'amount4' => array('value' => $order_info['money'] . '元',),
//                    'thing5' => array('value' => '您的本次服务已经完成，请及时支付您为完成的订单，支付完成后请到首页，提现押金！！！',),
//                );
//                $Wechat->newsSendout($openid, $template_id, $url, $data);
            }
        }
        //验证是否短时免单  $order_info['timelong_fenzhong'] > $order_info['hospital_freedt']
        if ($order_info['pay_status'] == 1 && $return['success'] == true) {
            //不短时免单
            //查询是否已经生成支付订单
            $pay = db('pay')->where(['order_id' => $order_info['id']])->find();
            if (!$pay) {
                //生成支付订单
                $pay_data = array(
                    'sn' => $this->getOrdersn('pay'),
                    'user_id' => $order_info['user_id'],
                    'types' => 2,
                    'status' => 1,
                    'money' => $order_info['money'],
                    'order_id' => $order_info['id'],
                    'createtime' => time(),
                    'updatetime' => time(),
                );
                $pay_id = db('pay')->insertGetId($pay_data);
                if (!$pay_id) {
                    $return['success'] = false;
                    $return['msg'] = '生成支付订单失败';
                }
            }
        }
        return $return;
    }

}
